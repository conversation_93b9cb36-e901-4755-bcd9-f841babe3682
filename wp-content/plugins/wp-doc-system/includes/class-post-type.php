<?php
/**
 * 自定义文章类型类
 *
 * @package WP_Doc_System
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 文档系统自定义文章类型类
 */
class WP_Doc_System_Post_Type {
    
    /**
     * 构造函数
     */
    public function __construct() {
        // 构造函数保持空白，在init方法中初始化
    }
    
    /**
     * 初始化
     */
    public function init() {
        add_action('init', array($this, 'register_post_type'));
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_meta_boxes'));
        add_filter('manage_doc_item_posts_columns', array($this, 'add_admin_columns'));
        add_action('manage_doc_item_posts_custom_column', array($this, 'display_admin_columns'), 10, 2);
    }
    
    /**
     * 注册自定义文章类型
     */
    public function register_post_type() {
        $labels = array(
            'name'                  => _x('文档项目', 'Post type general name', 'wp-doc-system'),
            'singular_name'         => _x('文档项目', 'Post type singular name', 'wp-doc-system'),
            'menu_name'             => _x('文档系统', 'Admin Menu text', 'wp-doc-system'),
            'name_admin_bar'        => _x('文档项目', 'Add New on Toolbar', 'wp-doc-system'),
            'add_new'               => __('新建', 'wp-doc-system'),
            'add_new_item'          => __('新建文档项目', 'wp-doc-system'),
            'new_item'              => __('新文档项目', 'wp-doc-system'),
            'edit_item'             => __('编辑文档项目', 'wp-doc-system'),
            'view_item'             => __('查看文档项目', 'wp-doc-system'),
            'all_items'             => __('所有项目', 'wp-doc-system'),
            'search_items'          => __('搜索文档项目', 'wp-doc-system'),
            'parent_item_colon'     => __('父级项目:', 'wp-doc-system'),
            'not_found'             => __('未找到文档项目。', 'wp-doc-system'),
            'not_found_in_trash'    => __('回收站中未找到文档项目。', 'wp-doc-system'),
            'featured_image'        => _x('文档封面图', 'Overrides the "Featured Image" phrase', 'wp-doc-system'),
            'set_featured_image'    => _x('设置封面图', 'Overrides the "Set featured image" phrase', 'wp-doc-system'),
            'remove_featured_image' => _x('移除封面图', 'Overrides the "Remove featured image" phrase', 'wp-doc-system'),
            'use_featured_image'    => _x('用作封面图', 'Overrides the "Use as featured image" phrase', 'wp-doc-system'),
            'archives'              => _x('文档归档', 'The post type archive label', 'wp-doc-system'),
            'insert_into_item'      => _x('插入到文档项目', 'Overrides the "Insert into post" phrase', 'wp-doc-system'),
            'uploaded_to_this_item' => _x('上传到此文档项目', 'Overrides the "Uploaded to this post" phrase', 'wp-doc-system'),
            'filter_items_list'     => _x('过滤文档项目列表', 'Screen reader text for the filter links', 'wp-doc-system'),
            'items_list_navigation' => _x('文档项目列表导航', 'Screen reader text for the pagination', 'wp-doc-system'),
            'items_list'            => _x('文档项目列表', 'Screen reader text for the items list', 'wp-doc-system'),
        );
        
        $args = array(
            'labels'             => $labels,
            'public'             => true,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'show_in_rest'       => true,
            'query_var'          => true,
            'rewrite'            => array('slug' => 'doc-item'),
            'capability_type'    => 'post',
            'has_archive'        => false,
            'hierarchical'       => true, // 支持父子关系
            'menu_position'      => 25,
            'menu_icon'          => 'dashicons-media-document',
            'supports'           => array('title', 'editor', 'excerpt', 'thumbnail', 'page-attributes'),
            'taxonomies'         => array(),
        );
        
        register_post_type('doc_item', $args);
    }
    
    /**
     * 添加元数据框
     */
    public function add_meta_boxes() {
        add_meta_box(
            'wp_doc_system_settings',
            __('文档设置', 'wp-doc-system'),
            array($this, 'render_settings_meta_box'),
            'doc_item',
            'side',
            'high'
        );
    }
    
    /**
     * 渲染设置元数据框
     */
    public function render_settings_meta_box($post) {
        // 添加nonce字段
        wp_nonce_field('wp_doc_system_meta_box', 'wp_doc_system_meta_box_nonce');
        
        // 获取当前值
        $doc_type = get_post_meta($post->ID, '_doc_type', true) ?: 'document';
        $doc_icon = get_post_meta($post->ID, '_doc_icon', true) ?: 'document';
        
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="doc_type"><?php _e('类型', 'wp-doc-system'); ?></label>
                </th>
                <td>
                    <select name="doc_type" id="doc_type" class="widefat">
                        <option value="document" <?php selected($doc_type, 'document'); ?>>
                            <?php _e('文档', 'wp-doc-system'); ?>
                        </option>
                        <option value="folder" <?php selected($doc_type, 'folder'); ?>>
                            <?php _e('文件夹', 'wp-doc-system'); ?>
                        </option>
                    </select>
                    <p class="description">
                        <?php _e('选择此项目的类型。文件夹可以包含其他项目，文档包含实际内容。', 'wp-doc-system'); ?>
                    </p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="doc_icon"><?php _e('图标', 'wp-doc-system'); ?></label>
                </th>
                <td>
                    <select name="doc_icon" id="doc_icon" class="widefat">
                        <option value="document" <?php selected($doc_icon, 'document'); ?>>📄 <?php _e('文档', 'wp-doc-system'); ?></option>
                        <option value="folder" <?php selected($doc_icon, 'folder'); ?>>📁 <?php _e('文件夹', 'wp-doc-system'); ?></option>
                        <option value="book" <?php selected($doc_icon, 'book'); ?>>📖 <?php _e('书籍', 'wp-doc-system'); ?></option>
                        <option value="guide" <?php selected($doc_icon, 'guide'); ?>>📋 <?php _e('指南', 'wp-doc-system'); ?></option>
                        <option value="api" <?php selected($doc_icon, 'api'); ?>>⚙️ <?php _e('API', 'wp-doc-system'); ?></option>
                        <option value="tutorial" <?php selected($doc_icon, 'tutorial'); ?>>🎓 <?php _e('教程', 'wp-doc-system'); ?></option>
                        <option value="faq" <?php selected($doc_icon, 'faq'); ?>>❓ <?php _e('FAQ', 'wp-doc-system'); ?></option>
                    </select>
                    <p class="description">
                        <?php _e('选择在目录树中显示的图标。', 'wp-doc-system'); ?>
                    </p>
                </td>
            </tr>
        </table>
        
        <script>
        jQuery(document).ready(function($) {
            // 根据类型自动设置图标
            $('#doc_type').change(function() {
                var type = $(this).val();
                var iconSelect = $('#doc_icon');
                
                if (type === 'folder') {
                    iconSelect.val('folder');
                } else if (type === 'document') {
                    iconSelect.val('document');
                }
            });
        });
        </script>
        <?php
    }
    
    /**
     * 保存元数据框数据
     */
    public function save_meta_boxes($post_id) {
        // 验证nonce
        if (!isset($_POST['wp_doc_system_meta_box_nonce']) || 
            !wp_verify_nonce($_POST['wp_doc_system_meta_box_nonce'], 'wp_doc_system_meta_box')) {
            return;
        }
        
        // 检查用户权限
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // 检查是否是自动保存
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        // 保存文档类型
        if (isset($_POST['doc_type'])) {
            $doc_type = sanitize_text_field($_POST['doc_type']);
            update_post_meta($post_id, '_doc_type', $doc_type);
        }
        
        // 保存图标
        if (isset($_POST['doc_icon'])) {
            $doc_icon = sanitize_text_field($_POST['doc_icon']);
            update_post_meta($post_id, '_doc_icon', $doc_icon);
        }
    }
    
    /**
     * 添加管理列表列
     */
    public function add_admin_columns($columns) {
        // 在标题后添加类型列
        $new_columns = array();
        foreach ($columns as $key => $value) {
            $new_columns[$key] = $value;
            if ($key === 'title') {
                $new_columns['doc_type'] = __('类型', 'wp-doc-system');
                $new_columns['doc_icon'] = __('图标', 'wp-doc-system');
            }
        }
        
        return $new_columns;
    }
    
    /**
     * 显示管理列表列内容
     */
    public function display_admin_columns($column, $post_id) {
        switch ($column) {
            case 'doc_type':
                $doc_type = get_post_meta($post_id, '_doc_type', true) ?: 'document';
                $type_labels = array(
                    'document' => __('文档', 'wp-doc-system'),
                    'folder' => __('文件夹', 'wp-doc-system'),
                );
                echo isset($type_labels[$doc_type]) ? $type_labels[$doc_type] : $doc_type;
                break;
                
            case 'doc_icon':
                $doc_icon = get_post_meta($post_id, '_doc_icon', true) ?: 'document';
                $icon_map = array(
                    'document' => '📄',
                    'folder' => '📁',
                    'book' => '📖',
                    'guide' => '📋',
                    'api' => '⚙️',
                    'tutorial' => '🎓',
                    'faq' => '❓',
                );
                echo isset($icon_map[$doc_icon]) ? $icon_map[$doc_icon] : '📄';
                break;
        }
    }
}
