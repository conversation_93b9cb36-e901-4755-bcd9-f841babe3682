<?php
/**
 * 权限管理类
 *
 * @package WP_Doc_System
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 文档系统权限类
 */
class WP_Doc_System_Permissions {
    
    /**
     * 构造函数
     */
    public function __construct() {
        add_action('init', array($this, 'add_capabilities'));
    }
    
    /**
     * 添加自定义权限
     */
    public function add_capabilities() {
        // 获取管理员角色
        $admin_role = get_role('administrator');
        $editor_role = get_role('editor');
        
        // 为管理员添加所有权限
        if ($admin_role) {
            $admin_role->add_cap('read_doc_item');
            $admin_role->add_cap('edit_doc_item');
            $admin_role->add_cap('edit_doc_items');
            $admin_role->add_cap('edit_others_doc_items');
            $admin_role->add_cap('publish_doc_items');
            $admin_role->add_cap('read_private_doc_items');
            $admin_role->add_cap('delete_doc_item');
            $admin_role->add_cap('delete_doc_items');
            $admin_role->add_cap('delete_others_doc_items');
            $admin_role->add_cap('delete_published_doc_items');
            $admin_role->add_cap('delete_private_doc_items');
            $admin_role->add_cap('edit_private_doc_items');
            $admin_role->add_cap('edit_published_doc_items');
        }
        
        // 为编辑者添加基本权限
        if ($editor_role) {
            $editor_role->add_cap('read_doc_item');
            $editor_role->add_cap('edit_doc_item');
            $editor_role->add_cap('edit_doc_items');
            $editor_role->add_cap('edit_others_doc_items');
            $editor_role->add_cap('publish_doc_items');
            $editor_role->add_cap('read_private_doc_items');
            $editor_role->add_cap('delete_doc_item');
            $editor_role->add_cap('delete_doc_items');
            $editor_role->add_cap('delete_others_doc_items');
            $editor_role->add_cap('delete_published_doc_items');
            $editor_role->add_cap('delete_private_doc_items');
            $editor_role->add_cap('edit_private_doc_items');
            $editor_role->add_cap('edit_published_doc_items');
        }
    }
    
    /**
     * 检查用户是否可以读取文档
     */
    public static function can_read_documents($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        // 如果未登录，根据设置决定
        if (!$user_id) {
            $options = get_option('wp_doc_system_options', array());
            return isset($options['public_access']) ? $options['public_access'] : true;
        }
        
        return user_can($user_id, 'read_doc_item') || user_can($user_id, 'read');
    }
    
    /**
     * 检查用户是否可以编辑文档
     */
    public static function can_edit_documents($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return false;
        }
        
        return user_can($user_id, 'edit_doc_items') || user_can($user_id, 'edit_posts');
    }
    
    /**
     * 检查用户是否可以删除文档
     */
    public static function can_delete_documents($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return false;
        }
        
        return user_can($user_id, 'delete_doc_items') || user_can($user_id, 'delete_posts');
    }
    
    /**
     * 检查用户是否可以管理文档系统
     */
    public static function can_manage_system($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return false;
        }
        
        return user_can($user_id, 'manage_options');
    }
}
