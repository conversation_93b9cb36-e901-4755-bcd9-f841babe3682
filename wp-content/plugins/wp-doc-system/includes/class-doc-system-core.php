<?php
/**
 * 核心功能类
 *
 * @package WP_Doc_System
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 文档系统核心类
 */
class WP_Doc_System_Core {
    
    /**
     * 构造函数
     */
    public function __construct() {
        // 初始化核心功能
    }
    
    /**
     * 获取文档树结构
     *
     * @param int $parent_id 父级ID
     * @return array 文档树数组
     */
    public function get_document_tree($parent_id = 0) {
        $args = array(
            'post_type' => 'doc_item',
            'post_status' => 'publish',
            'post_parent' => $parent_id,
            'orderby' => 'menu_order',
            'order' => 'ASC',
            'posts_per_page' => -1,
            'suppress_filters' => false,
        );

        $posts = get_posts($args);
        $tree = array();

        foreach ($posts as $post) {
            $doc_type = get_post_meta($post->ID, '_doc_type', true);
            $doc_icon = get_post_meta($post->ID, '_doc_icon', true);

            // 如果没有设置类型，根据是否有子项目来判断
            if (empty($doc_type)) {
                $children_count = wp_count_posts('doc_item');
                $has_children = get_posts(array(
                    'post_type' => 'doc_item',
                    'post_parent' => $post->ID,
                    'posts_per_page' => 1,
                ));
                $doc_type = !empty($has_children) ? 'folder' : 'document';
            }

            $item = array(
                'id' => $post->ID,
                'title' => $post->post_title,
                'type' => $doc_type,
                'icon' => $doc_icon ?: ($doc_type === 'folder' ? 'folder' : 'document'),
                'order' => $post->menu_order,
                'parent_id' => $post->post_parent,
                'slug' => $post->post_name,
                'excerpt' => $post->post_excerpt,
                'modified' => $post->post_modified,
                'children' => array(),
                'expanded' => false, // 前端展开状态
            );

            // 递归获取子项
            $children = $this->get_document_tree($post->ID);
            if (!empty($children)) {
                $item['children'] = $children;
                $item['has_children'] = true;
            } else {
                $item['has_children'] = false;
            }

            $tree[] = $item;
        }

        return $tree;
    }
    
    /**
     * 获取文档内容
     *
     * @param int $doc_id 文档ID
     * @return array|false 文档数据或false
     */
    public function get_document_content($doc_id) {
        $post = get_post($doc_id);

        if (!$post || $post->post_type !== 'doc_item') {
            return false;
        }

        // 检查文档状态
        if ($post->post_status !== 'publish') {
            return false;
        }

        $doc_type = get_post_meta($post->ID, '_doc_type', true) ?: 'document';
        $doc_icon = get_post_meta($post->ID, '_doc_icon', true) ?: 'document';

        // 获取父级信息
        $parent_info = null;
        if ($post->post_parent) {
            $parent_post = get_post($post->post_parent);
            if ($parent_post) {
                $parent_info = array(
                    'id' => $parent_post->ID,
                    'title' => $parent_post->post_title,
                    'type' => get_post_meta($parent_post->ID, '_doc_type', true) ?: 'folder',
                );
            }
        }

        // 获取子项目（如果是文件夹）
        $children = array();
        if ($doc_type === 'folder') {
            $child_posts = get_posts(array(
                'post_type' => 'doc_item',
                'post_parent' => $post->ID,
                'post_status' => 'publish',
                'orderby' => 'menu_order',
                'order' => 'ASC',
                'posts_per_page' => -1,
            ));

            foreach ($child_posts as $child) {
                $children[] = array(
                    'id' => $child->ID,
                    'title' => $child->post_title,
                    'type' => get_post_meta($child->ID, '_doc_type', true) ?: 'document',
                    'excerpt' => $child->post_excerpt,
                );
            }
        }

        return array(
            'id' => $post->ID,
            'title' => $post->post_title,
            'content' => apply_filters('the_content', $post->post_content),
            'excerpt' => $post->post_excerpt,
            'type' => $doc_type,
            'icon' => $doc_icon,
            'slug' => $post->post_name,
            'status' => $post->post_status,
            'created' => $post->post_date,
            'modified' => $post->post_modified,
            'author' => get_the_author_meta('display_name', $post->post_author),
            'author_id' => $post->post_author,
            'parent' => $parent_info,
            'children' => $children,
            'menu_order' => $post->menu_order,
            'word_count' => str_word_count(strip_tags($post->post_content)),
            'reading_time' => ceil(str_word_count(strip_tags($post->post_content)) / 200), // 假设每分钟200字
        );
    }
    
    /**
     * 搜索文档
     *
     * @param string $query 搜索关键词
     * @return array 搜索结果
     */
    public function search_documents($query) {
        $args = array(
            'post_type' => 'doc_item',
            'post_status' => 'publish',
            's' => sanitize_text_field($query),
            'posts_per_page' => 20,
        );
        
        $posts = get_posts($args);
        $results = array();
        
        foreach ($posts as $post) {
            $results[] = array(
                'id' => $post->ID,
                'title' => $post->post_title,
                'excerpt' => wp_trim_words($post->post_content, 20),
                'type' => get_post_meta($post->ID, '_doc_type', true) ?: 'document',
                'url' => get_permalink($post->ID),
            );
        }
        
        return $results;
    }
    
    /**
     * 验证用户权限
     *
     * @param string $action 操作类型
     * @param int $post_id 文章ID
     * @return bool 是否有权限
     */
    public function check_permission($action, $post_id = 0) {
        switch ($action) {
            case 'read':
                return true; // 所有人都可以读取
                
            case 'edit':
            case 'create':
            case 'delete':
                return current_user_can('edit_posts');
                
            default:
                return false;
        }
    }
}
