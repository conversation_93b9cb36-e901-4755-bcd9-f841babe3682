# WP Doc System

一个现代化的前后端分离WordPress文档系统插件，使用Vue.js和Tailwind CSS构建。

## 🌟 特性

- **前后端分离架构**：Vue 3 + WordPress REST API
- **现代化UI**：使用Tailwind CSS构建响应式界面
- **层级文档管理**：支持文件夹和文档的层级结构
- **实时搜索**：快速搜索文档内容
- **CDN资源**：无需本地构建，直接使用CDN资源
- **短代码支持**：轻松在任何页面嵌入文档系统
- **权限控制**：基于WordPress用户权限系统

## 📦 安装

1. 将插件文件夹上传到 `/wp-content/plugins/` 目录
2. 在WordPress管理后台激活插件
3. 访问 "文档系统" 菜单开始使用

## 🚀 快速开始

### 1. 创建文档结构

在WordPress管理后台：
- 访问 "文档系统" → "新建文档"
- 选择类型：文件夹或文档
- 设置父级关系建立层级结构

### 2. 使用短代码

在任何页面或文章中使用短代码：

```
[doc_system]
```

### 3. 短代码参数

```
[doc_system height="600px" theme="default" show_search="true"]
```

参数说明：
- `height`: 容器高度（默认：600px）
- `theme`: 主题样式（default/dark）
- `show_search`: 是否显示搜索框（true/false）
- `show_toolbar`: 是否显示工具栏（true/false）
- `root_folder`: 根文件夹ID（默认：0）

## 🔧 开发和测试

### 测试页面

插件提供了多个测试页面：

1. **完整功能测试**：`/wp-content/plugins/wp-doc-system/test-complete.php`
2. **API测试**：`/wp-content/plugins/wp-doc-system/test-api.php`
3. **短代码测试**：`/wp-content/plugins/wp-doc-system/test-shortcode.php`

### API端点

- `GET /wp-json/doc-system/v1/tree` - 获取完整目录树
- `GET /wp-json/doc-system/v1/documents/{id}` - 获取文档内容
- `GET /wp-json/doc-system/v1/search?q={query}` - 搜索文档
- `POST /wp-json/doc-system/v1/folders` - 创建文件夹
- `PUT /wp-json/doc-system/v1/folders/{id}` - 更新文件夹
- `DELETE /wp-json/doc-system/v1/items/{id}` - 删除项目

## 📁 文件结构

```
wp-doc-system/
├── wp-doc-system.php          # 主插件文件
├── includes/                  # PHP类文件
│   ├── class-doc-system-core.php
│   ├── class-post-type.php
│   ├── class-rest-api.php
│   ├── class-admin.php
│   └── class-permissions.php
├── assets/                    # 前端资源
│   ├── admin/                 # 后台资源
│   └── frontend/              # 前端资源
├── templates/                 # 模板文件
│   └── doc-system-app.php
├── test-*.php                 # 测试文件
└── README.md                  # 说明文档
```

## 🎨 自定义样式

插件使用Tailwind CSS，您可以通过以下方式自定义样式：

1. 修改模板文件中的CSS变量
2. 使用WordPress的`wp_add_inline_style`添加自定义样式
3. 创建自定义主题

## 🔒 权限说明

- **查看文档**：所有用户（可配置）
- **编辑文档**：具有`edit_posts`权限的用户
- **管理系统**：具有`manage_options`权限的用户

## 🐛 故障排除

### 常见问题

1. **Vue应用不显示**
   - 检查CDN资源是否正常加载
   - 查看浏览器控制台错误信息
   - 确认插件已正确激活

2. **API调用失败**
   - 检查WordPress REST API是否启用
   - 确认用户权限设置
   - 查看服务器错误日志

3. **样式显示异常**
   - 确认Tailwind CSS CDN正常加载
   - 检查主题CSS冲突
   - 清除缓存

### 调试模式

在`wp-config.php`中启用调试：

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## 📄 许可证

GPL v2 or later

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请通过以下方式联系：
- GitHub Issues
- WordPress支持论坛
- 插件作者邮箱

---

**版本**: 1.0.0  
**兼容性**: WordPress 5.0+, PHP 7.4+  
**测试环境**: WordPress 6.8, PHP 8.0+
