<?php
/**
 * 强制激活和修复插件
 */

// 加载WordPress
require_once '../../../wp-load.php';

// 检查权限
if (!current_user_can('activate_plugins')) {
    wp_die('权限不足');
}

echo '<h1>🔧 强制激活 WP Doc System</h1>';

// 1. 直接在数据库中激活插件
echo '<h2>💾 数据库激活</h2>';
$plugin_path = 'wp-doc-system/wp-doc-system.php';
$active_plugins = get_option('active_plugins', array());

if (!in_array($plugin_path, $active_plugins)) {
    $active_plugins[] = $plugin_path;
    update_option('active_plugins', $active_plugins);
    echo '<p>✅ 插件已在数据库中激活</p>';
} else {
    echo '<p>ℹ️ 插件已在数据库中激活</p>';
}

// 2. 强制加载插件文件
echo '<h2>📁 加载插件文件</h2>';
$plugin_file = WP_PLUGIN_DIR . '/wp-doc-system/wp-doc-system.php';
if (file_exists($plugin_file)) {
    include_once $plugin_file;
    echo '<p>✅ 插件文件已加载</p>';
} else {
    echo '<p>❌ 插件文件不存在</p>';
    exit;
}

// 3. 直接注册文章类型（不依赖类）
echo '<h2>📝 直接注册文章类型</h2>';

// 定义标签
$labels = array(
    'name' => '文档项目',
    'singular_name' => '文档项目',
    'menu_name' => '文档系统',
    'name_admin_bar' => '文档项目',
    'add_new' => '新建',
    'add_new_item' => '新建文档项目',
    'new_item' => '新文档项目',
    'edit_item' => '编辑文档项目',
    'view_item' => '查看文档项目',
    'all_items' => '所有项目',
    'search_items' => '搜索文档项目',
    'parent_item_colon' => '父级项目:',
    'not_found' => '未找到文档项目。',
    'not_found_in_trash' => '回收站中未找到文档项目。',
);

// 定义参数
$args = array(
    'labels' => $labels,
    'public' => true,
    'publicly_queryable' => true,
    'show_ui' => true,
    'show_in_menu' => true,
    'show_in_rest' => true,
    'query_var' => true,
    'rewrite' => array('slug' => 'doc-item'),
    'capability_type' => 'post',
    'has_archive' => false,
    'hierarchical' => true,
    'menu_position' => 25,
    'menu_icon' => 'dashicons-media-document',
    'supports' => array('title', 'editor', 'excerpt', 'thumbnail', 'page-attributes'),
);

// 注册文章类型
$result = register_post_type('doc_item', $args);

if (is_wp_error($result)) {
    echo '<p>❌ 文章类型注册失败：' . $result->get_error_message() . '</p>';
} else {
    echo '<p>✅ 文章类型注册成功</p>';
}

// 4. 刷新重写规则
flush_rewrite_rules();
echo '<p>✅ 重写规则已刷新</p>';

// 5. 验证注册结果
echo '<h2>🔍 验证结果</h2>';
echo '<ul>';
echo '<li>插件激活状态：' . (is_plugin_active($plugin_path) ? '✅ 已激活' : '❌ 未激活') . '</li>';
echo '<li>文章类型存在：' . (post_type_exists('doc_item') ? '✅ 已注册' : '❌ 未注册') . '</li>';

// 获取文章类型对象
$post_type_object = get_post_type_object('doc_item');
if ($post_type_object) {
    echo '<li>文章类型对象：✅ 已获取</li>';
    echo '<li>菜单显示：' . ($post_type_object->show_in_menu ? '✅ 是' : '❌ 否') . '</li>';
    echo '<li>UI显示：' . ($post_type_object->show_ui ? '✅ 是' : '❌ 否') . '</li>';
} else {
    echo '<li>文章类型对象：❌ 未获取</li>';
}

echo '</ul>';

// 6. 创建测试数据
echo '<h2>📚 创建测试数据</h2>';
if (post_type_exists('doc_item')) {
    // 检查现有数据
    $existing = get_posts(array(
        'post_type' => 'doc_item',
        'posts_per_page' => 1,
    ));
    
    if (empty($existing)) {
        // 创建测试文件夹
        $folder_data = array(
            'post_title' => '用户指南',
            'post_type' => 'doc_item',
            'post_status' => 'publish',
            'post_content' => '这是用户指南文件夹，包含所有用户相关的文档。',
            'post_excerpt' => '用户指南和教程',
            'menu_order' => 1,
        );
        
        $folder_id = wp_insert_post($folder_data);
        
        if ($folder_id && !is_wp_error($folder_id)) {
            // 添加元数据
            add_post_meta($folder_id, '_doc_type', 'folder');
            add_post_meta($folder_id, '_doc_icon', 'folder');
            
            echo '<p>✅ 测试文件夹创建成功 (ID: ' . $folder_id . ')</p>';
            
            // 创建测试文档
            $doc_data = array(
                'post_title' => '快速开始',
                'post_type' => 'doc_item',
                'post_status' => 'publish',
                'post_parent' => $folder_id,
                'post_content' => '<h2>欢迎使用文档系统</h2><p>这是一个功能完整的文档管理系统。</p><h3>主要功能</h3><ul><li>层级文档管理</li><li>实时搜索</li><li>响应式界面</li><li>REST API支持</li></ul>',
                'post_excerpt' => '快速开始使用指南',
                'menu_order' => 1,
            );
            
            $doc_id = wp_insert_post($doc_data);
            
            if ($doc_id && !is_wp_error($doc_id)) {
                add_post_meta($doc_id, '_doc_type', 'document');
                add_post_meta($doc_id, '_doc_icon', 'document');
                
                echo '<p>✅ 测试文档创建成功 (ID: ' . $doc_id . ')</p>';
            }
        }
    } else {
        echo '<p>ℹ️ 已存在文档数据</p>';
    }
} else {
    echo '<p>❌ 无法创建测试数据，文章类型未注册</p>';
}

// 7. 提供操作链接
echo '<h2>🎯 现在您可以：</h2>';
echo '<div style="background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;">';

if (post_type_exists('doc_item')) {
    echo '<h3>✅ 修复成功！</h3>';
    echo '<ul style="list-style: none; padding: 0;">';
    echo '<li style="margin: 10px 0;"><a href="' . admin_url('edit.php?post_type=doc_item') . '" style="display: inline-block; padding: 10px 15px; background: #0073aa; color: white; text-decoration: none; border-radius: 4px;">📝 管理文档项目</a></li>';
    echo '<li style="margin: 10px 0;"><a href="' . admin_url('post-new.php?post_type=doc_item') . '" style="display: inline-block; padding: 10px 15px; background: #00a32a; color: white; text-decoration: none; border-radius: 4px;">➕ 新建文档项目</a></li>';
    echo '<li style="margin: 10px 0;"><a href="test-complete.php" style="display: inline-block; padding: 10px 15px; background: #8b5cf6; color: white; text-decoration: none; border-radius: 4px;">🧪 测试功能</a></li>';
    echo '</ul>';
} else {
    echo '<h3>❌ 修复失败</h3>';
    echo '<p>请检查WordPress错误日志或联系开发者。</p>';
}

echo '</div>';

?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    max-width: 900px;
    margin: 20px auto;
    padding: 20px;
    line-height: 1.6;
    background: #f5f5f5;
}
h1, h2, h3 { color: #333; }
ul { background: #fff; padding: 15px; border-radius: 5px; margin: 10px 0; }
li { margin: 5px 0; }
a { color: #0073aa; text-decoration: none; }
a:hover { text-decoration: underline; }
p { margin: 8px 0; }
</style>
