<?php
/**
 * 完整功能测试页面
 *
 * 访问: /wp-content/plugins/wp-doc-system/test-complete.php
 */

// 加载WordPress
require_once '../../../wp-load.php';

// 检查是否是管理员
if (!current_user_can('manage_options')) {
    wp_die('权限不足');
}

// 处理测试操作
$action = $_GET['action'] ?? '';
$message = '';

if ($action === 'create_test_data') {
    $message = create_test_data();
} elseif ($action === 'clear_test_data') {
    $message = clear_test_data();
}

/**
 * 创建测试数据
 */
function create_test_data() {
    // 创建根文件夹
    $folder1_id = wp_insert_post(array(
        'post_title' => '用户指南',
        'post_type' => 'doc_item',
        'post_status' => 'publish',
        'post_content' => '这是用户指南的介绍内容。',
        'post_excerpt' => '包含所有用户相关的文档和教程',
        'menu_order' => 1,
    ));
    
    if ($folder1_id) {
        update_post_meta($folder1_id, '_doc_type', 'folder');
        update_post_meta($folder1_id, '_doc_icon', 'guide');
    }
    
    // 创建子文件夹
    $folder2_id = wp_insert_post(array(
        'post_title' => '快速开始',
        'post_type' => 'doc_item',
        'post_status' => 'publish',
        'post_parent' => $folder1_id,
        'post_content' => '快速开始指南，帮助您快速上手。',
        'post_excerpt' => '新用户必读的快速入门指南',
        'menu_order' => 1,
    ));
    
    if ($folder2_id) {
        update_post_meta($folder2_id, '_doc_type', 'folder');
        update_post_meta($folder2_id, '_doc_icon', 'tutorial');
    }
    
    // 创建文档
    $doc1_id = wp_insert_post(array(
        'post_title' => '安装指南',
        'post_type' => 'doc_item',
        'post_status' => 'publish',
        'post_parent' => $folder2_id,
        'post_content' => '<h2>安装步骤</h2><p>1. 下载插件文件</p><p>2. 上传到WordPress</p><p>3. 激活插件</p><p>4. 配置设置</p>',
        'post_excerpt' => '详细的安装步骤说明',
        'menu_order' => 1,
    ));
    
    if ($doc1_id) {
        update_post_meta($doc1_id, '_doc_type', 'document');
        update_post_meta($doc1_id, '_doc_icon', 'document');
    }
    
    $doc2_id = wp_insert_post(array(
        'post_title' => '基本配置',
        'post_type' => 'doc_item',
        'post_status' => 'publish',
        'post_parent' => $folder2_id,
        'post_content' => '<h2>配置选项</h2><p>在这里您可以了解如何配置插件的各种选项。</p><ul><li>基本设置</li><li>显示选项</li><li>权限配置</li></ul>',
        'post_excerpt' => '插件的基本配置说明',
        'menu_order' => 2,
    ));
    
    if ($doc2_id) {
        update_post_meta($doc2_id, '_doc_type', 'document');
        update_post_meta($doc2_id, '_doc_icon', 'document');
    }
    
    // 创建API文档文件夹
    $api_folder_id = wp_insert_post(array(
        'post_title' => 'API文档',
        'post_type' => 'doc_item',
        'post_status' => 'publish',
        'post_content' => 'API接口文档和开发者资源。',
        'post_excerpt' => '开发者API接口文档',
        'menu_order' => 2,
    ));
    
    if ($api_folder_id) {
        update_post_meta($api_folder_id, '_doc_type', 'folder');
        update_post_meta($api_folder_id, '_doc_icon', 'api');
    }
    
    $api_doc_id = wp_insert_post(array(
        'post_title' => 'REST API',
        'post_type' => 'doc_item',
        'post_status' => 'publish',
        'post_parent' => $api_folder_id,
        'post_content' => '<h2>REST API 端点</h2><h3>获取目录树</h3><code>GET /wp-json/doc-system/v1/tree</code><h3>获取文档内容</h3><code>GET /wp-json/doc-system/v1/documents/{id}</code>',
        'post_excerpt' => 'REST API接口说明',
        'menu_order' => 1,
    ));
    
    if ($api_doc_id) {
        update_post_meta($api_doc_id, '_doc_type', 'document');
        update_post_meta($api_doc_id, '_doc_icon', 'api');
    }
    
    return '✅ 测试数据创建成功！创建了 ' . count(array_filter([$folder1_id, $folder2_id, $doc1_id, $doc2_id, $api_folder_id, $api_doc_id])) . ' 个项目。';
}

/**
 * 清除测试数据
 */
function clear_test_data() {
    $posts = get_posts(array(
        'post_type' => 'doc_item',
        'post_status' => 'any',
        'posts_per_page' => -1,
    ));
    
    $count = 0;
    foreach ($posts as $post) {
        if (wp_delete_post($post->ID, true)) {
            $count++;
        }
    }
    
    return '🗑️ 已清除 ' . $count . ' 个测试项目。';
}

// 获取当前状态
$plugin_active = is_plugin_active('wp-doc-system/wp-doc-system.php');
$post_type_exists = post_type_exists('doc_item');
$doc_count = wp_count_posts('doc_item');
$total_docs = $doc_count->publish ?? 0;

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>完整功能测试 - WP Doc System</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f1;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .status-item {
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .status-success { background: #d1ecf1; color: #0c5460; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-error { background: #f8d7da; color: #721c24; }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #0073aa;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #005a87; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .message {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .demo-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .demo-header {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🚀 WP Doc System 完整功能测试</h1>
            <p>测试插件的所有核心功能，包括数据创建、API调用和前端显示。</p>
        </div>
        
        <?php if ($message): ?>
        <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <div class="card">
            <h2>📊 系统状态</h2>
            <div class="status-grid">
                <div class="status-item <?php echo $plugin_active ? 'status-success' : 'status-error'; ?>">
                    <div style="font-size: 24px; margin-bottom: 8px;">
                        <?php echo $plugin_active ? '✅' : '❌'; ?>
                    </div>
                    <div><strong>插件状态</strong></div>
                    <div><?php echo $plugin_active ? '已激活' : '未激活'; ?></div>
                </div>
                
                <div class="status-item <?php echo $post_type_exists ? 'status-success' : 'status-error'; ?>">
                    <div style="font-size: 24px; margin-bottom: 8px;">
                        <?php echo $post_type_exists ? '✅' : '❌'; ?>
                    </div>
                    <div><strong>文章类型</strong></div>
                    <div><?php echo $post_type_exists ? '已注册' : '未注册'; ?></div>
                </div>
                
                <div class="status-item <?php echo $total_docs > 0 ? 'status-success' : 'status-warning'; ?>">
                    <div style="font-size: 24px; margin-bottom: 8px;">
                        <?php echo $total_docs > 0 ? '📚' : '📭'; ?>
                    </div>
                    <div><strong>文档数量</strong></div>
                    <div><?php echo $total_docs; ?> 个项目</div>
                </div>
                
                <div class="status-item status-success">
                    <div style="font-size: 24px; margin-bottom: 8px;">🕒</div>
                    <div><strong>测试时间</strong></div>
                    <div><?php echo date('H:i:s'); ?></div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>🛠️ 测试操作</h2>
            <div style="margin-bottom: 20px;">
                <a href="?action=create_test_data" class="btn btn-success">创建测试数据</a>
                <a href="?action=clear_test_data" class="btn btn-danger" onclick="return confirm('确定要清除所有测试数据吗？')">清除测试数据</a>
                <a href="test-api.php" class="btn">API测试</a>
                <a href="test-shortcode.php" class="btn">短代码测试</a>
            </div>
        </div>
        
        <?php if ($total_docs > 0): ?>
        <div class="card">
            <h2>📱 前端显示测试</h2>
            <div class="demo-container">
                <div class="demo-header">
                    短代码: [doc_system height="500px"]
                </div>
                <div>
                    <?php echo do_shortcode('[doc_system height="500px"]'); ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>🔗 相关链接</h2>
            <div>
                <a href="<?php echo admin_url('admin.php?page=wp-doc-system'); ?>" class="btn">插件管理</a>
                <a href="<?php echo admin_url('edit.php?post_type=doc_item'); ?>" class="btn">文档管理</a>
                <a href="<?php echo admin_url('post-new.php?post_type=doc_item'); ?>" class="btn">新建文档</a>
                <a href="<?php echo rest_url('doc-system/v1/tree'); ?>" class="btn" target="_blank">API端点</a>
            </div>
        </div>
    </div>
    
    <script>
        // 自动刷新状态
        setTimeout(function() {
            if (window.location.search.includes('action=')) {
                window.location.href = window.location.pathname;
            }
        }, 3000);
    </script>
</body>
</html>
