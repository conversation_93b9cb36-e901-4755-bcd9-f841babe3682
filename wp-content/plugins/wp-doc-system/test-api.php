<?php
/**
 * API测试文件 - 仅用于开发测试
 * 
 * 访问: /wp-content/plugins/wp-doc-system/test-api.php
 */

// 加载WordPress
require_once '../../../wp-load.php';

// 检查是否是管理员
if (!current_user_can('manage_options')) {
    wp_die('权限不足');
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>WP Doc System API 测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .result { background: #f9f9f9; padding: 10px; margin: 10px 0; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>WP Doc System API 测试</h1>
    
    <div class="test-section">
        <h2>1. 插件状态检查</h2>
        <div class="result">
            <?php
            // 检查插件是否激活
            if (is_plugin_active('wp-doc-system/wp-doc-system.php')) {
                echo '<span class="success">✓ 插件已激活</span>';
            } else {
                echo '<span class="error">✗ 插件未激活</span>';
            }
            ?>
        </div>
        
        <div class="result">
            <?php
            // 检查自定义文章类型
            if (post_type_exists('doc_item')) {
                echo '<span class="success">✓ 自定义文章类型已注册</span>';
            } else {
                echo '<span class="error">✗ 自定义文章类型未注册</span>';
            }
            ?>
        </div>
        
        <div class="result">
            <?php
            // 检查类文件
            $classes = [
                'WP_Doc_System' => '主类',
                'WP_Doc_System_Core' => '核心类',
                'WP_Doc_System_Post_Type' => '文章类型类',
                'WP_Doc_System_REST_API' => 'REST API类',
                'WP_Doc_System_Admin' => '管理员类',
            ];
            
            foreach ($classes as $class => $name) {
                if (class_exists($class)) {
                    echo '<span class="success">✓ ' . $name . ' 已加载</span><br>';
                } else {
                    echo '<span class="error">✗ ' . $name . ' 未加载</span><br>';
                }
            }
            ?>
        </div>
    </div>
    
    <div class="test-section">
        <h2>2. 创建测试数据</h2>
        <button onclick="createTestData()">创建测试数据</button>
        <div id="create-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. API端点测试</h2>
        <button onclick="testTreeAPI()">测试目录树API</button>
        <button onclick="testDocumentAPI()">测试文档API</button>
        <button onclick="testSearchAPI()">测试搜索API</button>
        <div id="api-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 前端组件测试</h2>
        <button onclick="testFrontend()">测试前端组件</button>
        <div id="frontend-result" class="result"></div>
        
        <!-- 前端组件容器 -->
        <div id="doc-system-test" style="height: 400px; border: 1px solid #ccc; margin-top: 10px;">
            <!-- 这里将显示Vue组件 -->
        </div>
    </div>

    <script>
        // API基础URL
        const apiUrl = '<?php echo rest_url('doc-system/v1/'); ?>';
        const nonce = '<?php echo wp_create_nonce('wp_rest'); ?>';
        
        // 创建测试数据
        async function createTestData() {
            const result = document.getElementById('create-result');
            result.innerHTML = '正在创建测试数据...';
            
            try {
                // 创建根文件夹
                const folder1 = await fetch(apiUrl + 'folders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': nonce
                    },
                    body: JSON.stringify({
                        title: '用户指南',
                        parent_id: 0
                    })
                });
                
                const folder1Data = await folder1.json();
                
                if (folder1Data.id) {
                    // 创建子文件夹
                    const folder2 = await fetch(apiUrl + 'folders', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-WP-Nonce': nonce
                        },
                        body: JSON.stringify({
                            title: '快速开始',
                            parent_id: folder1Data.id
                        })
                    });
                    
                    result.innerHTML = '<span class="success">✓ 测试数据创建成功</span>';
                } else {
                    result.innerHTML = '<span class="error">✗ 创建失败: ' + JSON.stringify(folder1Data) + '</span>';
                }
                
            } catch (error) {
                result.innerHTML = '<span class="error">✗ 创建失败: ' + error.message + '</span>';
            }
        }
        
        // 测试目录树API
        async function testTreeAPI() {
            const result = document.getElementById('api-result');
            result.innerHTML = '正在测试目录树API...';
            
            try {
                const response = await fetch(apiUrl + 'tree', {
                    headers: {
                        'X-WP-Nonce': nonce
                    }
                });
                
                const data = await response.json();
                result.innerHTML = '<span class="success">✓ 目录树API测试成功</span><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
            } catch (error) {
                result.innerHTML = '<span class="error">✗ 目录树API测试失败: ' + error.message + '</span>';
            }
        }
        
        // 测试文档API
        async function testDocumentAPI() {
            const result = document.getElementById('api-result');
            result.innerHTML = '正在测试文档API...';
            
            try {
                // 先获取一个文档ID
                const treeResponse = await fetch(apiUrl + 'tree', {
                    headers: { 'X-WP-Nonce': nonce }
                });
                const tree = await treeResponse.json();
                
                if (tree.length > 0) {
                    const docId = tree[0].id;
                    const docResponse = await fetch(apiUrl + 'documents/' + docId, {
                        headers: { 'X-WP-Nonce': nonce }
                    });
                    const doc = await docResponse.json();
                    
                    result.innerHTML = '<span class="success">✓ 文档API测试成功</span><pre>' + JSON.stringify(doc, null, 2) + '</pre>';
                } else {
                    result.innerHTML = '<span class="error">✗ 没有找到测试文档</span>';
                }
                
            } catch (error) {
                result.innerHTML = '<span class="error">✗ 文档API测试失败: ' + error.message + '</span>';
            }
        }
        
        // 测试搜索API
        async function testSearchAPI() {
            const result = document.getElementById('api-result');
            result.innerHTML = '正在测试搜索API...';
            
            try {
                const response = await fetch(apiUrl + 'search?q=指南', {
                    headers: { 'X-WP-Nonce': nonce }
                });
                const data = await response.json();
                
                result.innerHTML = '<span class="success">✓ 搜索API测试成功</span><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
            } catch (error) {
                result.innerHTML = '<span class="error">✗ 搜索API测试失败: ' + error.message + '</span>';
            }
        }
        
        // 测试前端组件
        function testFrontend() {
            const result = document.getElementById('frontend-result');
            const container = document.getElementById('doc-system-test');
            
            // 检查依赖
            if (typeof Vue === 'undefined') {
                result.innerHTML = '<span class="error">✗ Vue.js 未加载</span>';
                return;
            }
            
            if (typeof axios === 'undefined') {
                result.innerHTML = '<span class="error">✗ Axios 未加载</span>';
                return;
            }
            
            // 创建简单的测试组件
            const { createApp } = Vue;
            
            const TestApp = {
                data() {
                    return {
                        message: '前端组件测试成功！',
                        apiData: null
                    }
                },
                async mounted() {
                    try {
                        const response = await axios.get(apiUrl + 'tree', {
                            headers: { 'X-WP-Nonce': nonce }
                        });
                        this.apiData = response.data;
                    } catch (error) {
                        console.error('API调用失败:', error);
                    }
                },
                template: `
                    <div class="p-4">
                        <h3>{{ message }}</h3>
                        <div v-if="apiData">
                            <p>API数据加载成功，共 {{ apiData.length }} 个项目</p>
                            <ul>
                                <li v-for="item in apiData" :key="item.id">
                                    {{ item.title }} ({{ item.type }})
                                </li>
                            </ul>
                        </div>
                        <div v-else>
                            <p>正在加载API数据...</p>
                        </div>
                    </div>
                `
            };
            
            createApp(TestApp).mount('#doc-system-test');
            result.innerHTML = '<span class="success">✓ 前端组件已挂载</span>';
        }
    </script>
    
    <!-- 加载CDN资源 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</body>
</html>
