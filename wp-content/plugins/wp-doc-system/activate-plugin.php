<?php
/**
 * 插件激活脚本
 * 用于测试和激活插件
 */

// 加载WordPress
require_once '../../../wp-load.php';

// 检查权限
if (!current_user_can('activate_plugins')) {
    wp_die('权限不足');
}

// 激活插件
$plugin_file = 'wp-doc-system/wp-doc-system.php';

if (!is_plugin_active($plugin_file)) {
    $result = activate_plugin($plugin_file);
    
    if (is_wp_error($result)) {
        echo '<h1>❌ 插件激活失败</h1>';
        echo '<p>错误信息：' . $result->get_error_message() . '</p>';
    } else {
        echo '<h1>✅ 插件激活成功</h1>';
        echo '<p>WP Doc System 插件已成功激活！</p>';
        
        // 刷新重写规则
        flush_rewrite_rules();
        
        echo '<h2>📋 激活后检查</h2>';
        echo '<ul>';
        echo '<li>插件状态：' . (is_plugin_active($plugin_file) ? '✅ 已激活' : '❌ 未激活') . '</li>';
        echo '<li>自定义文章类型：' . (post_type_exists('doc_item') ? '✅ 已注册' : '❌ 未注册') . '</li>';
        
        // 检查类是否存在
        $classes = [
            'WP_Doc_System' => '主类',
            'WP_Doc_System_Core' => '核心类',
            'WP_Doc_System_Post_Type' => '文章类型类',
            'WP_Doc_System_REST_API' => 'REST API类',
        ];
        
        foreach ($classes as $class => $name) {
            echo '<li>' . $name . '：' . (class_exists($class) ? '✅ 已加载' : '❌ 未加载') . '</li>';
        }
        
        echo '</ul>';
        
        echo '<h2>🔗 下一步</h2>';
        echo '<p><a href="test-complete.php">前往完整测试页面</a></p>';
        echo '<p><a href="' . admin_url('admin.php?page=wp-doc-system') . '">前往插件管理页面</a></p>';
        echo '<p><a href="' . admin_url('edit.php?post_type=doc_item') . '">管理文档项目</a></p>';
    }
} else {
    echo '<h1>ℹ️ 插件已激活</h1>';
    echo '<p>WP Doc System 插件已经处于激活状态。</p>';
    
    echo '<h2>📋 当前状态</h2>';
    echo '<ul>';
    echo '<li>插件状态：✅ 已激活</li>';
    echo '<li>自定义文章类型：' . (post_type_exists('doc_item') ? '✅ 已注册' : '❌ 未注册') . '</li>';
    echo '</ul>';
    
    echo '<h2>🔗 快速链接</h2>';
    echo '<p><a href="test-complete.php">完整测试页面</a></p>';
    echo '<p><a href="' . admin_url('admin.php?page=wp-doc-system') . '">插件管理页面</a></p>';
}

?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    max-width: 800px;
    margin: 40px auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2 { color: #333; }
ul { background: #f9f9f9; padding: 20px; border-radius: 5px; }
a { color: #0073aa; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
