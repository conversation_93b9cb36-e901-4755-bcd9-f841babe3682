<?php
/**
 * 前端界面演示页面
 */

// 加载WordPress
require_once '../../../wp-load.php';

// 获取页面标题
$page_title = '文档系统前端演示';

// 获取WordPress头部
get_header();
?>

<div class="demo-page-container" style="max-width: 1200px; margin: 0 auto; padding: 20px;">
    
    <div class="demo-header" style="text-align: center; margin-bottom: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <h1 style="color: #333; margin-bottom: 10px;">📚 文档系统前端演示</h1>
        <p style="color: #666; font-size: 16px;">这是WP Doc System插件的前端界面展示</p>
    </div>

    <!-- 方法1: 基本短代码 -->
    <div class="demo-section" style="margin-bottom: 40px;">
        <h2 style="color: #333; border-bottom: 2px solid #0073aa; padding-bottom: 10px;">🎯 基本显示</h2>
        <p>使用短代码：<code>[doc_system]</code></p>
        <div style="border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
            <?php echo do_shortcode('[doc_system]'); ?>
        </div>
    </div>

    <!-- 方法2: 自定义高度 -->
    <div class="demo-section" style="margin-bottom: 40px;">
        <h2 style="color: #333; border-bottom: 2px solid #0073aa; padding-bottom: 10px;">📏 自定义高度</h2>
        <p>使用短代码：<code>[doc_system height="400px"]</code></p>
        <div style="border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
            <?php echo do_shortcode('[doc_system height="400px"]'); ?>
        </div>
    </div>

    <!-- 方法3: 完整参数 -->
    <div class="demo-section" style="margin-bottom: 40px;">
        <h2 style="color: #333; border-bottom: 2px solid #0073aa; padding-bottom: 10px;">⚙️ 完整参数</h2>
        <p>使用短代码：<code>[doc_system height="500px" theme="default" show_search="true" show_toolbar="true"]</code></p>
        <div style="border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
            <?php echo do_shortcode('[doc_system height="500px" theme="default" show_search="true" show_toolbar="true"]'); ?>
        </div>
    </div>

    <!-- 使用说明 -->
    <div class="demo-instructions" style="background: #e7f3ff; padding: 20px; border-radius: 8px; margin-top: 40px;">
        <h2 style="color: #333; margin-top: 0;">📖 使用说明</h2>
        
        <h3>🔧 如何在您的网站中使用：</h3>
        <ol>
            <li><strong>在页面中使用</strong>：编辑任何页面，添加短代码 <code>[doc_system]</code></li>
            <li><strong>在文章中使用</strong>：编辑任何文章，添加短代码 <code>[doc_system]</code></li>
            <li><strong>在小工具中使用</strong>：在外观 → 小工具中添加"文本"小工具，输入短代码</li>
            <li><strong>在模板中使用</strong>：在PHP模板文件中使用 <code>&lt;?php echo do_shortcode('[doc_system]'); ?&gt;</code></li>
        </ol>

        <h3>⚙️ 短代码参数：</h3>
        <ul>
            <li><code>height</code> - 容器高度，如：<code>height="600px"</code></li>
            <li><code>theme</code> - 主题样式，如：<code>theme="default"</code> 或 <code>theme="dark"</code></li>
            <li><code>show_search</code> - 是否显示搜索框，如：<code>show_search="true"</code></li>
            <li><code>show_toolbar</code> - 是否显示工具栏，如：<code>show_toolbar="true"</code></li>
            <li><code>root_folder</code> - 根文件夹ID，如：<code>root_folder="123"</code></li>
        </ul>

        <h3>🎨 自定义样式：</h3>
        <p>您可以通过CSS自定义文档系统的外观：</p>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto;"><code>.wp-doc-system-app {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.wp-doc-system-sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}</code></pre>
    </div>

    <!-- 快速链接 -->
    <div class="demo-links" style="text-align: center; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <h3 style="margin-top: 0;">🔗 相关链接</h3>
        <a href="<?php echo admin_url('edit.php?post_type=doc_item'); ?>" style="display: inline-block; margin: 5px; padding: 10px 20px; background: #0073aa; color: white; text-decoration: none; border-radius: 4px;">📝 管理文档</a>
        <a href="<?php echo admin_url('post-new.php?post_type=doc_item'); ?>" style="display: inline-block; margin: 5px; padding: 10px 20px; background: #00a32a; color: white; text-decoration: none; border-radius: 4px;">➕新建文档</a>
        <a href="test-complete.php" style="display: inline-block; margin: 5px; padding: 10px 20px; background: #8b5cf6; color: white; text-decoration: none; border-radius: 4px;">🧪 功能测试</a>
    </div>

</div>

<style>
/* 确保演示页面样式正确 */
.demo-page-container {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    line-height: 1.6;
}

.demo-page-container code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.demo-page-container pre {
    font-size: 13px;
    line-height: 1.4;
}

.demo-section {
    margin-bottom: 30px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .demo-page-container {
        padding: 10px;
    }
    
    .demo-links a {
        display: block;
        margin: 10px 0;
    }
}
</style>

<?php
// 获取WordPress底部
get_footer();
?>
