<?php
/**
 * 文档系统前端应用模板
 *
 * @package WP_Doc_System
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 获取短代码属性
$height = isset($atts['height']) ? esc_attr($atts['height']) : '600px';
$theme = isset($atts['theme']) ? esc_attr($atts['theme']) : 'default';
?>

<div class="wp-doc-system-wrapper" data-theme="<?php echo $theme; ?>">
    <!-- Tailwind CSS 配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Vue应用容器 -->
    <div id="wp-doc-system-app" 
         class="wp-doc-system-app border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm"
         style="height: <?php echo $height; ?>;">
        
        <!-- 加载状态 -->
        <div class="flex items-center justify-center h-full" id="wp-doc-system-loading">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p class="text-gray-600"><?php _e('正在加载文档系统...', 'wp-doc-system'); ?></p>
            </div>
        </div>
        
        <!-- 错误状态 -->
        <div class="hidden flex items-center justify-center h-full" id="wp-doc-system-error">
            <div class="text-center text-red-600">
                <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <p class="text-lg font-semibold mb-2"><?php _e('加载失败', 'wp-doc-system'); ?></p>
                <p class="text-sm"><?php _e('请检查网络连接或刷新页面重试', 'wp-doc-system'); ?></p>
                <button onclick="location.reload()" class="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                    <?php _e('重新加载', 'wp-doc-system'); ?>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 样式覆盖 -->
    <style>
        .wp-doc-system-wrapper {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        .wp-doc-system-app {
            position: relative;
            overflow: hidden;
        }
        
        /* 确保Vue应用占满容器 */
        .wp-doc-system-app > div:not(#wp-doc-system-loading):not(#wp-doc-system-error) {
            height: 100%;
        }
        
        /* 自定义滚动条 */
        .wp-doc-system-app ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        
        .wp-doc-system-app ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        .wp-doc-system-app ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        .wp-doc-system-app ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .wp-doc-system-app .wp-doc-system-sidebar {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                z-index: 10;
                box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            }
            
            .wp-doc-system-app .wp-doc-system-sidebar.collapsed {
                transform: translateX(-100%);
            }
        }
        
        /* 动画效果 */
        .wp-doc-system-app .tree-node {
            transition: all 0.2s ease;
        }

        .wp-doc-system-app .tree-node:hover {
            transform: translateX(2px);
        }

        /* Vue过渡动画 */
        .slide-fade-enter-active {
            transition: all 0.3s ease-out;
        }

        .slide-fade-leave-active {
            transition: all 0.2s cubic-bezier(1.0, 0.5, 0.8, 1.0);
        }

        .slide-fade-enter-from,
        .slide-fade-leave-to {
            transform: translateY(-10px);
            opacity: 0;
        }

        /* 旋转动画 */
        .rotate-90 {
            transform: rotate(90deg);
        }
        
        /* 加载动画 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .wp-doc-system-app .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        /* 主题样式 */
        .wp-doc-system-wrapper[data-theme="dark"] {
            --bg-primary: #1f2937;
            --bg-secondary: #374151;
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --border-color: #4b5563;
        }
        
        .wp-doc-system-wrapper[data-theme="dark"] .wp-doc-system-app {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            border-color: var(--border-color);
        }
        
        /* 打印样式 */
        @media print {
            .wp-doc-system-app .wp-doc-system-sidebar {
                display: none;
            }
            
            .wp-doc-system-app {
                height: auto !important;
                border: none;
                box-shadow: none;
            }
        }
    </style>
    
    <!-- 错误处理脚本 -->
    <script>
        // 检查依赖是否加载
        function checkDependencies() {
            const loading = document.getElementById('wp-doc-system-loading');
            const error = document.getElementById('wp-doc-system-error');
            
            // 检查Vue
            if (typeof Vue === 'undefined') {
                console.error('Vue.js 未加载');
                showError('Vue.js 加载失败');
                return false;
            }
            
            // 检查axios
            if (typeof axios === 'undefined') {
                console.error('Axios 未加载');
                showError('Axios 加载失败');
                return false;
            }
            
            // 检查wpDocSystem配置
            if (typeof wpDocSystem === 'undefined') {
                console.error('wpDocSystem 配置未找到');
                showError('配置加载失败');
                return false;
            }
            
            return true;
        }
        
        function showError(message) {
            const loading = document.getElementById('wp-doc-system-loading');
            const error = document.getElementById('wp-doc-system-error');
            
            if (loading) loading.style.display = 'none';
            if (error) {
                error.style.display = 'flex';
                const errorText = error.querySelector('p:last-of-type');
                if (errorText) errorText.textContent = message;
            }
        }
        
        // 延迟检查，确保所有脚本都已加载
        setTimeout(function() {
            if (!checkDependencies()) {
                return;
            }
            
            // 如果Vue应用成功挂载，隐藏加载状态
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        const app = document.getElementById('wp-doc-system-app');
                        const loading = document.getElementById('wp-doc-system-loading');
                        
                        // 检查是否有Vue应用的内容
                        if (app && app.children.length > 2) { // 除了loading和error元素
                            if (loading) loading.style.display = 'none';
                            observer.disconnect();
                        }
                    }
                });
            });
            
            observer.observe(document.getElementById('wp-doc-system-app'), {
                childList: true,
                subtree: true
            });
            
        }, 1000);
        
        // 全局错误处理
        window.addEventListener('error', function(e) {
            if (e.filename && e.filename.includes('doc-system')) {
                console.error('文档系统错误:', e.error);
                showError('应用运行时错误');
            }
        });
    </script>
</div>
