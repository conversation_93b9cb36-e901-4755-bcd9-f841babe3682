<?php
/**
 * 短代码测试页面
 * 
 * 访问: /wp-content/plugins/wp-doc-system/test-shortcode.php
 */

// 加载WordPress
require_once '../../../wp-load.php';

// 检查是否是管理员
if (!current_user_can('manage_options')) {
    wp_die('权限不足');
}

// 模拟短代码渲染
$shortcode_output = do_shortcode('[doc_system height="500px" theme="default"]');

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>短代码测试 - WP Doc System</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .shortcode-demo {
            margin: 20px 0;
        }
        .code-block {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 WP Doc System 短代码测试</h1>
            <p>测试文档系统的短代码功能和前端显示效果</p>
        </div>
        
        <div class="test-info">
            <h3>📋 测试信息</h3>
            <ul>
                <li><strong>插件状态：</strong><?php echo is_plugin_active('wp-doc-system/wp-doc-system.php') ? '✅ 已激活' : '❌ 未激活'; ?></li>
                <li><strong>短代码：</strong><code>[doc_system height="500px" theme="default"]</code></li>
                <li><strong>测试时间：</strong><?php echo date('Y-m-d H:i:s'); ?></li>
            </ul>
        </div>
        
        <div class="shortcode-demo">
            <h3>📱 短代码渲染效果</h3>
            <div class="code-block">
                [doc_system height="500px" theme="default"]
            </div>
            
            <!-- 短代码输出 -->
            <div style="border: 2px dashed #ccc; padding: 10px; margin: 20px 0;">
                <?php echo $shortcode_output; ?>
            </div>
        </div>
        
        <div style="margin-top: 30px;">
            <h3>🔧 其他短代码选项</h3>
            <div class="code-block">
                [doc_system height="400px" theme="default"]<br>
                [doc_system height="600px" theme="dark"]<br>
                [doc_system height="800px"]
            </div>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
            <h3>🛠️ 调试信息</h3>
            <div style="font-size: 12px; color: #666;">
                <p><strong>WordPress版本：</strong><?php echo get_bloginfo('version'); ?></p>
                <p><strong>PHP版本：</strong><?php echo PHP_VERSION; ?></p>
                <p><strong>插件版本：</strong><?php echo defined('WP_DOC_SYSTEM_VERSION') ? WP_DOC_SYSTEM_VERSION : '未知'; ?></p>
                <p><strong>当前主题：</strong><?php echo wp_get_theme()->get('Name'); ?></p>
            </div>
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
            <a href="test-api.php" style="display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 4px;">
                🔍 API测试页面
            </a>
            <a href="<?php echo admin_url('admin.php?page=wp-doc-system'); ?>" style="display: inline-block; padding: 10px 20px; background: #00a32a; color: white; text-decoration: none; border-radius: 4px; margin-left: 10px;">
                ⚙️ 插件管理
            </a>
        </div>
    </div>
    
    <script>
        // 检查前端资源是否正确加载
        setTimeout(function() {
            const checks = {
                'Vue.js': typeof Vue !== 'undefined',
                'Axios': typeof axios !== 'undefined',
                'Tailwind CSS': document.querySelector('script[src*="tailwindcss"]') !== null,
                'Doc System App': document.getElementById('wp-doc-system-app') !== null
            };
            
            console.log('🔍 前端资源检查结果:');
            for (const [name, loaded] of Object.entries(checks)) {
                console.log(`${loaded ? '✅' : '❌'} ${name}: ${loaded ? '已加载' : '未加载'}`);
            }
            
            // 检查Vue应用是否正确挂载
            if (typeof Vue !== 'undefined') {
                const app = document.getElementById('wp-doc-system-app');
                if (app && app.children.length > 0) {
                    console.log('✅ Vue应用已成功挂载');
                } else {
                    console.log('❌ Vue应用挂载失败');
                }
            }
        }, 2000);
    </script>
</body>
</html>
