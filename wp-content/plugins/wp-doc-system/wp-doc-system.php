<?php
/**
 * Plugin Name: WP Doc System
 * Plugin URI: https://github.com/your-username/wp-doc-system
 * Description: 一个现代化的前后端分离文档系统插件，使用Vue.js和Tailwind CSS构建，提供左侧目录树和右侧内容展示的文档管理系统。
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://your-website.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: wp-doc-system
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.8
 * Requires PHP: 7.4
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 定义插件常量
define('WP_DOC_SYSTEM_VERSION', '1.0.0');
define('WP_DOC_SYSTEM_PLUGIN_FILE', __FILE__);
define('WP_DOC_SYSTEM_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WP_DOC_SYSTEM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WP_DOC_SYSTEM_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * 主插件类
 */
class WP_Doc_System {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
    }
    
    /**
     * 初始化钩子
     */
    private function init_hooks() {
        // 插件激活和停用钩子
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // WordPress初始化钩子
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // 管理员界面钩子
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
        
        // 前端钩子
        add_action('wp_enqueue_scripts', array($this, 'frontend_enqueue_scripts'));
        add_shortcode('doc_system', array($this, 'render_doc_system_shortcode'));
    }
    
    /**
     * 加载依赖文件
     */
    private function load_dependencies() {
        // 核心类文件
        require_once WP_DOC_SYSTEM_PLUGIN_DIR . 'includes/class-doc-system-core.php';
        require_once WP_DOC_SYSTEM_PLUGIN_DIR . 'includes/class-post-type.php';
        require_once WP_DOC_SYSTEM_PLUGIN_DIR . 'includes/class-rest-api.php';
        require_once WP_DOC_SYSTEM_PLUGIN_DIR . 'includes/class-admin.php';
        require_once WP_DOC_SYSTEM_PLUGIN_DIR . 'includes/class-permissions.php';
    }
    
    /**
     * 插件激活
     */
    public function activate() {
        // 创建自定义文章类型
        $post_type = new WP_Doc_System_Post_Type();
        $post_type->register_post_type();
        
        // 刷新重写规则
        flush_rewrite_rules();
        
        // 设置默认选项
        $this->set_default_options();
    }
    
    /**
     * 插件停用
     */
    public function deactivate() {
        // 刷新重写规则
        flush_rewrite_rules();
    }
    
    /**
     * WordPress初始化
     */
    public function init() {
        // 初始化自定义文章类型
        $post_type = new WP_Doc_System_Post_Type();
        $post_type->init();
        
        // 初始化REST API
        $rest_api = new WP_Doc_System_REST_API();
        $rest_api->init();
    }
    
    /**
     * 加载文本域
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'wp-doc-system',
            false,
            dirname(WP_DOC_SYSTEM_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * 添加管理员菜单
     */
    public function add_admin_menu() {
        $admin = new WP_Doc_System_Admin();
        $admin->add_menu_pages();
    }
    
    /**
     * 加载管理员脚本和样式
     */
    public function admin_enqueue_scripts($hook) {
        // 只在插件页面加载
        if (strpos($hook, 'doc-system') !== false) {
            wp_enqueue_style(
                'wp-doc-system-admin',
                WP_DOC_SYSTEM_PLUGIN_URL . 'assets/admin/admin.css',
                array(),
                WP_DOC_SYSTEM_VERSION
            );
            
            wp_enqueue_script(
                'wp-doc-system-admin',
                WP_DOC_SYSTEM_PLUGIN_URL . 'assets/admin/admin.js',
                array('jquery'),
                WP_DOC_SYSTEM_VERSION,
                true
            );
        }
    }
    
    /**
     * 加载前端脚本和样式
     */
    public function frontend_enqueue_scripts() {
        // 只在需要时加载
        global $post;
        if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'doc_system')) {
            $this->enqueue_frontend_assets();
        }
    }
    
    /**
     * 加载前端资源
     */
    private function enqueue_frontend_assets() {
        // Vue 3 CDN
        wp_enqueue_script(
            'vue-js',
            'https://unpkg.com/vue@3/dist/vue.global.js',
            array(),
            '3.0.0',
            true
        );
        
        // Axios CDN
        wp_enqueue_script(
            'axios-js',
            'https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js',
            array(),
            '1.0.0',
            true
        );
        
        // Tailwind CSS CDN
        wp_enqueue_script(
            'tailwind-config',
            'https://cdn.tailwindcss.com',
            array(),
            '3.0.0',
            false
        );
        
        // 主应用脚本
        wp_enqueue_script(
            'wp-doc-system-app',
            WP_DOC_SYSTEM_PLUGIN_URL . 'assets/frontend/doc-system.js',
            array('vue-js', 'axios-js'),
            WP_DOC_SYSTEM_VERSION,
            true
        );
        
        // 传递数据到前端
        wp_localize_script('wp-doc-system-app', 'wpDocSystem', array(
            'apiUrl' => rest_url('doc-system/v1/'),
            'nonce' => wp_create_nonce('wp_rest'),
            'ajaxUrl' => admin_url('admin-ajax.php'),
        ));
    }
    
    /**
     * 渲染文档系统短代码
     */
    public function render_doc_system_shortcode($atts) {
        // 确保前端资源已加载
        $this->enqueue_frontend_assets();

        // 解析短代码属性
        $atts = shortcode_atts(array(
            'height' => '600px',
            'theme' => 'default',
            'show_search' => 'true',
            'show_toolbar' => 'true',
            'root_folder' => '0',
            'max_width' => '100%',
        ), $atts, 'doc_system');

        // 验证属性
        $atts['height'] = $this->validate_css_value($atts['height'], '600px');
        $atts['max_width'] = $this->validate_css_value($atts['max_width'], '100%');
        $atts['theme'] = in_array($atts['theme'], array('default', 'dark')) ? $atts['theme'] : 'default';
        $atts['show_search'] = filter_var($atts['show_search'], FILTER_VALIDATE_BOOLEAN);
        $atts['show_toolbar'] = filter_var($atts['show_toolbar'], FILTER_VALIDATE_BOOLEAN);
        $atts['root_folder'] = intval($atts['root_folder']);

        // 检查是否有文档数据
        $core = new WP_Doc_System_Core();
        $tree = $core->get_document_tree($atts['root_folder']);

        // 如果没有数据，显示提示信息
        if (empty($tree)) {
            return $this->render_empty_state();
        }

        // 包含模板文件
        ob_start();
        include WP_DOC_SYSTEM_PLUGIN_DIR . 'templates/doc-system-app.php';
        return ob_get_clean();
    }

    /**
     * 验证CSS值
     */
    private function validate_css_value($value, $default) {
        // 简单的CSS值验证
        if (preg_match('/^[\d.]+(px|em|rem|%|vh|vw)$/', $value)) {
            return $value;
        }
        return $default;
    }

    /**
     * 渲染空状态
     */
    private function render_empty_state() {
        $admin_url = admin_url('admin.php?page=wp-doc-system');

        return '<div class="wp-doc-system-empty" style="
            padding: 40px 20px;
            text-align: center;
            border: 2px dashed #ccc;
            border-radius: 8px;
            background: #f9f9f9;
            color: #666;
        ">
            <div style="font-size: 48px; margin-bottom: 16px;">📚</div>
            <h3 style="margin: 0 0 8px 0; color: #333;">暂无文档内容</h3>
            <p style="margin: 0 0 16px 0;">请先创建一些文档或文件夹。</p>
            ' . (current_user_can('edit_posts') ?
                '<a href="' . esc_url($admin_url) . '" style="
                    display: inline-block;
                    padding: 8px 16px;
                    background: #0073aa;
                    color: white;
                    text-decoration: none;
                    border-radius: 4px;
                ">前往管理</a>' : ''
            ) . '
        </div>';
    }
    
    /**
     * 设置默认选项
     */
    private function set_default_options() {
        $default_options = array(
            'documents_per_page' => 10,
            'default_sort' => 'menu_order',
            'enable_search' => true,
            'theme_color' => '#3b82f6',
        );
        
        add_option('wp_doc_system_options', $default_options);
    }
}

// 初始化插件
function wp_doc_system_init() {
    return WP_Doc_System::get_instance();
}

// 启动插件
wp_doc_system_init();
