<?php
/**
 * 独立的前端界面演示页面
 */

// 加载WordPress
require_once '../../../wp-load.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档系统前端演示 - WP Doc System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f7fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            background: white;
            padding: 40px 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .demo-section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .shortcode-info {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #3498db;
        }
        
        .doc-system-wrapper {
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .instructions {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-top: 40px;
        }
        
        .instructions h2 {
            margin-bottom: 20px;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 10px;
        }
        
        .instructions ul, .instructions ol {
            margin-left: 20px;
            margin-bottom: 15px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .instructions code {
            background: rgba(255,255,255,0.2);
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        
        .quick-links {
            text-align: center;
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 8px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }
        
        .btn-success { background: #27ae60; }
        .btn-success:hover { background: #229954; }
        
        .btn-purple { background: #8e44ad; }
        .btn-purple:hover { background: #7d3c98; }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .demo-section {
                padding: 20px;
            }
            
            .btn {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- 页面头部 -->
        <div class="header">
            <h1>📚 文档系统前端演示</h1>
            <p>WP Doc System - 现代化的WordPress文档管理系统</p>
        </div>

        <!-- 基本演示 -->
        <div class="demo-section">
            <h2>🎯 基本显示效果</h2>
            <div class="shortcode-info">
                短代码：[doc_system]
            </div>
            <div class="doc-system-wrapper">
                <?php echo do_shortcode('[doc_system]'); ?>
            </div>
        </div>

        <!-- 自定义高度演示 -->
        <div class="demo-section">
            <h2>📏 自定义高度 (400px)</h2>
            <div class="shortcode-info">
                短代码：[doc_system height="400px"]
            </div>
            <div class="doc-system-wrapper">
                <?php echo do_shortcode('[doc_system height="400px"]'); ?>
            </div>
        </div>

        <!-- 完整参数演示 -->
        <div class="demo-section">
            <h2>⚙️ 完整参数配置</h2>
            <div class="shortcode-info">
                短代码：[doc_system height="500px" theme="default" show_search="true" show_toolbar="true"]
            </div>
            <div class="doc-system-wrapper">
                <?php echo do_shortcode('[doc_system height="500px" theme="default" show_search="true" show_toolbar="true"]'); ?>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="instructions">
            <h2>📖 如何在您的网站中使用</h2>
            
            <h3>🚀 快速开始：</h3>
            <ol>
                <li><strong>创建页面</strong>：在WordPress后台创建新页面</li>
                <li><strong>添加短代码</strong>：在页面内容中添加 <code>[doc_system]</code></li>
                <li><strong>发布页面</strong>：保存并发布页面</li>
                <li><strong>查看效果</strong>：访问页面查看文档系统界面</li>
            </ol>

            <h3>⚙️ 短代码参数：</h3>
            <ul>
                <li><code>height="600px"</code> - 设置容器高度</li>
                <li><code>theme="default"</code> - 设置主题样式 (default/dark)</li>
                <li><code>show_search="true"</code> - 是否显示搜索框</li>
                <li><code>show_toolbar="true"</code> - 是否显示工具栏</li>
                <li><code>root_folder="123"</code> - 指定根文件夹ID</li>
            </ul>

            <h3>📍 使用位置：</h3>
            <ul>
                <li><strong>页面内容</strong>：在页面编辑器中直接添加短代码</li>
                <li><strong>文章内容</strong>：在文章编辑器中添加短代码</li>
                <li><strong>小工具</strong>：在外观 → 小工具 → 文本小工具中添加</li>
                <li><strong>模板文件</strong>：在PHP文件中使用 <code>&lt;?php echo do_shortcode('[doc_system]'); ?&gt;</code></li>
            </ul>

            <h3>🎨 自定义样式：</h3>
            <p>您可以通过添加自定义CSS来修改文档系统的外观。在外观 → 自定义 → 额外CSS中添加：</p>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; margin-top: 10px;">
                <code style="display: block; white-space: pre-wrap;">.wp-doc-system-app {
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.wp-doc-system-sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}</code>
            </div>
        </div>

        <!-- 快速链接 -->
        <div class="quick-links">
            <h2 style="margin-bottom: 20px; color: #2c3e50;">🔗 管理和测试</h2>
            <a href="<?php echo admin_url('edit.php?post_type=doc_item'); ?>" class="btn">📝 管理文档</a>
            <a href="<?php echo admin_url('post-new.php?post_type=doc_item'); ?>" class="btn btn-success">➕ 新建文档</a>
            <a href="test-complete.php" class="btn btn-purple">🧪 功能测试</a>
            <a href="<?php echo admin_url('admin.php?page=wp-doc-system'); ?>" class="btn">⚙️ 插件设置</a>
        </div>

    </div>

    <script>
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📚 WP Doc System 前端演示页面已加载');
            
            // 检查Vue应用是否正确加载
            setTimeout(function() {
                const apps = document.querySelectorAll('#wp-doc-system-app');
                apps.forEach(function(app, index) {
                    if (app.children.length > 2) {
                        console.log(`✅ 文档系统应用 ${index + 1} 加载成功`);
                    } else {
                        console.log(`❌ 文档系统应用 ${index + 1} 加载失败`);
                    }
                });
            }, 2000);
        });
    </script>
</body>
</html>
