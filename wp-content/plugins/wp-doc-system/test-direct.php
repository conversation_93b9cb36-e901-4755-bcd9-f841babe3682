<?php
/**
 * 直接测试插件功能
 * 不依赖WordPress激活状态
 */

// 加载WordPress
require_once '../../../wp-load.php';

// 直接加载插件文件
require_once 'wp-doc-system.php';

echo '<h1>🧪 WP Doc System 直接测试</h1>';

// 测试类是否加载
echo '<h2>📋 类加载测试</h2>';
$classes = [
    'WP_Doc_System' => '主类',
    'WP_Doc_System_Core' => '核心类',
    'WP_Doc_System_Post_Type' => '文章类型类',
    'WP_Doc_System_REST_API' => 'REST API类',
    'WP_Doc_System_Admin' => '管理员类',
];

echo '<ul>';
foreach ($classes as $class => $name) {
    $loaded = class_exists($class);
    echo '<li>' . $name . '：' . ($loaded ? '✅ 已加载' : '❌ 未加载') . '</li>';
    
    if ($loaded) {
        try {
            $instance = new $class();
            echo '<li style="margin-left: 20px;">实例化：✅ 成功</li>';
        } catch (Exception $e) {
            echo '<li style="margin-left: 20px;">实例化：❌ 失败 - ' . $e->getMessage() . '</li>';
        }
    }
}
echo '</ul>';

// 测试自定义文章类型注册
echo '<h2>📝 文章类型测试</h2>';
try {
    $post_type = new WP_Doc_System_Post_Type();
    $post_type->register_post_type();
    
    echo '<ul>';
    echo '<li>文章类型注册：✅ 成功</li>';
    echo '<li>doc_item 存在：' . (post_type_exists('doc_item') ? '✅ 是' : '❌ 否') . '</li>';
    echo '</ul>';
} catch (Exception $e) {
    echo '<p>❌ 文章类型注册失败：' . $e->getMessage() . '</p>';
}

// 测试核心功能
echo '<h2>🔧 核心功能测试</h2>';
try {
    $core = new WP_Doc_System_Core();
    $tree = $core->get_document_tree();
    
    echo '<ul>';
    echo '<li>核心类实例化：✅ 成功</li>';
    echo '<li>获取文档树：✅ 成功</li>';
    echo '<li>文档数量：' . count($tree) . ' 个</li>';
    echo '</ul>';
    
    if (!empty($tree)) {
        echo '<h3>📚 现有文档</h3>';
        echo '<ul>';
        foreach ($tree as $item) {
            echo '<li>' . $item['title'] . ' (' . $item['type'] . ')</li>';
        }
        echo '</ul>';
    }
} catch (Exception $e) {
    echo '<p>❌ 核心功能测试失败：' . $e->getMessage() . '</p>';
}

// 测试REST API
echo '<h2>🌐 REST API测试</h2>';
try {
    $api = new WP_Doc_System_REST_API();
    $api->register_routes();
    
    echo '<ul>';
    echo '<li>REST API类实例化：✅ 成功</li>';
    echo '<li>路由注册：✅ 成功</li>';
    echo '</ul>';
    
    // 测试API端点
    $rest_url = rest_url('doc-system/v1/tree');
    echo '<p>API端点：<a href="' . $rest_url . '" target="_blank">' . $rest_url . '</a></p>';
    
} catch (Exception $e) {
    echo '<p>❌ REST API测试失败：' . $e->getMessage() . '</p>';
}

// 测试短代码
echo '<h2>📄 短代码测试</h2>';
try {
    $plugin = WP_Doc_System::get_instance();
    
    // 模拟短代码调用
    $shortcode_output = $plugin->render_doc_system_shortcode(array());
    
    echo '<ul>';
    echo '<li>短代码渲染：✅ 成功</li>';
    echo '<li>输出长度：' . strlen($shortcode_output) . ' 字符</li>';
    echo '</ul>';
    
    echo '<h3>📱 短代码输出预览</h3>';
    echo '<div style="border: 1px solid #ccc; padding: 10px; max-height: 200px; overflow: auto;">';
    echo htmlspecialchars(substr($shortcode_output, 0, 500)) . '...';
    echo '</div>';
    
} catch (Exception $e) {
    echo '<p>❌ 短代码测试失败：' . $e->getMessage() . '</p>';
}

// 创建测试数据
echo '<h2>🗂️ 创建测试数据</h2>';
if (isset($_GET['create_data'])) {
    try {
        // 创建测试文件夹
        $folder_id = wp_insert_post(array(
            'post_title' => '测试文件夹',
            'post_type' => 'doc_item',
            'post_status' => 'publish',
            'post_content' => '这是一个测试文件夹',
        ));
        
        if ($folder_id && !is_wp_error($folder_id)) {
            update_post_meta($folder_id, '_doc_type', 'folder');
            update_post_meta($folder_id, '_doc_icon', 'folder');
            
            // 创建测试文档
            $doc_id = wp_insert_post(array(
                'post_title' => '测试文档',
                'post_type' => 'doc_item',
                'post_status' => 'publish',
                'post_parent' => $folder_id,
                'post_content' => '<h2>测试文档</h2><p>这是一个测试文档的内容。</p>',
            ));
            
            if ($doc_id && !is_wp_error($doc_id)) {
                update_post_meta($doc_id, '_doc_type', 'document');
                update_post_meta($doc_id, '_doc_icon', 'document');
                
                echo '<p>✅ 测试数据创建成功！</p>';
                echo '<ul>';
                echo '<li>文件夹ID：' . $folder_id . '</li>';
                echo '<li>文档ID：' . $doc_id . '</li>';
                echo '</ul>';
            } else {
                echo '<p>❌ 文档创建失败</p>';
            }
        } else {
            echo '<p>❌ 文件夹创建失败</p>';
        }
    } catch (Exception $e) {
        echo '<p>❌ 数据创建失败：' . $e->getMessage() . '</p>';
    }
} else {
    echo '<p><a href="?create_data=1">点击创建测试数据</a></p>';
}

echo '<h2>🔗 相关链接</h2>';
echo '<ul>';
echo '<li><a href="test-complete.php">完整测试页面</a></li>';
echo '<li><a href="test-api.php">API测试页面</a></li>';
echo '<li><a href="test-shortcode.php">短代码测试页面</a></li>';
echo '<li><a href="' . admin_url('plugins.php') . '">WordPress插件页面</a></li>';
echo '</ul>';

?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    line-height: 1.6;
    background: #f5f5f5;
}
h1, h2, h3 { color: #333; }
ul { background: #fff; padding: 15px; border-radius: 5px; margin: 10px 0; }
a { color: #0073aa; text-decoration: none; }
a:hover { text-decoration: underline; }
div { margin: 10px 0; }
</style>
