/**
 * WP Doc System 前端Vue应用
 */

// 等待Vue和其他依赖加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 确保Vue已加载
    if (typeof Vue === 'undefined') {
        console.error('Vue.js 未加载');
        return;
    }
    
    // 确保axios已加载
    if (typeof axios === 'undefined') {
        console.error('Axios 未加载');
        return;
    }
    
    // 创建Vue应用
    const { createApp, ref, reactive, onMounted, computed } = Vue;
    
    const DocSystemApp = {
        setup() {
            // 响应式数据
            const state = reactive({
                treeData: [],
                currentDocument: null,
                loading: false,
                searchQuery: '',
                sidebarCollapsed: false,
                error: null
            });
            
            // 计算属性
            const filteredTree = computed(() => {
                if (!state.searchQuery) {
                    return state.treeData;
                }
                // 简单的搜索过滤逻辑
                return filterTree(state.treeData, state.searchQuery.toLowerCase());
            });
            
            // 方法
            const loadTree = async () => {
                state.loading = true;
                state.error = null;
                
                try {
                    const response = await axios.get(wpDocSystem.apiUrl + 'tree', {
                        headers: {
                            'X-WP-Nonce': wpDocSystem.nonce
                        }
                    });
                    
                    state.treeData = response.data;
                } catch (error) {
                    console.error('加载目录树失败:', error);
                    state.error = '加载目录树失败，请刷新页面重试。';
                } finally {
                    state.loading = false;
                }
            };
            
            const loadDocument = async (docId) => {
                if (!docId) return;
                
                state.loading = true;
                state.error = null;
                
                try {
                    const response = await axios.get(wpDocSystem.apiUrl + 'documents/' + docId, {
                        headers: {
                            'X-WP-Nonce': wpDocSystem.nonce
                        }
                    });
                    
                    state.currentDocument = response.data;
                } catch (error) {
                    console.error('加载文档失败:', error);
                    state.error = '加载文档失败，请重试。';
                } finally {
                    state.loading = false;
                }
            };
            
            const selectItem = (item) => {
                if (item.type === 'document') {
                    loadDocument(item.id);
                } else {
                    // 文件夹点击时展开/折叠
                    item.expanded = !item.expanded;
                }
            };
            
            const toggleSidebar = () => {
                state.sidebarCollapsed = !state.sidebarCollapsed;
            };

            const getDocumentIcon = (doc) => {
                const iconMap = {
                    'folder': '📁',
                    'document': '📄',
                    'book': '📖',
                    'guide': '📋',
                    'api': '⚙️',
                    'tutorial': '🎓',
                    'faq': '❓'
                };
                return iconMap[doc.icon] || iconMap[doc.type] || '📄';
            };

            const formatDate = (dateString) => {
                const date = new Date(dateString);
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            };

            const printDocument = () => {
                window.print();
            };

            const copyLink = async () => {
                try {
                    const url = window.location.href + '#doc-' + state.currentDocument.id;
                    await navigator.clipboard.writeText(url);
                    // 这里可以添加一个提示消息
                    console.log('链接已复制到剪贴板');
                } catch (err) {
                    console.error('复制链接失败:', err);
                }
            };
            
            const filterTree = (tree, query) => {
                return tree.filter(item => {
                    const matchesTitle = item.title.toLowerCase().includes(query);
                    const hasMatchingChildren = item.children && filterTree(item.children, query).length > 0;
                    
                    if (matchesTitle || hasMatchingChildren) {
                        return {
                            ...item,
                            children: item.children ? filterTree(item.children, query) : []
                        };
                    }
                    return false;
                }).filter(Boolean);
            };
            
            // 生命周期
            onMounted(() => {
                loadTree();
            });
            
            return {
                state,
                filteredTree,
                loadTree,
                loadDocument,
                selectItem,
                toggleSidebar,
                getDocumentIcon,
                formatDate,
                printDocument,
                copyLink
            };
        },
        
        template: `
            <div class="wp-doc-system-app h-full flex bg-gray-50">
                <!-- 侧边栏 -->
                <div :class="['wp-doc-system-sidebar transition-all duration-300', state.sidebarCollapsed ? 'w-0' : 'w-80']" 
                     class="bg-white border-r border-gray-200 overflow-hidden">
                    <div class="p-4 border-b border-gray-200">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">文档目录</h3>
                            <button @click="toggleSidebar" 
                                    class="p-1 rounded hover:bg-gray-100">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        
                        <!-- 搜索框 -->
                        <div class="relative">
                            <input v-model="state.searchQuery" 
                                   type="text" 
                                   placeholder="搜索文档..."
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    
                    <!-- 目录树 -->
                    <div class="p-4 overflow-y-auto" style="height: calc(100% - 140px);">
                        <div v-if="state.loading" class="text-center py-8">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                            <p class="mt-2 text-gray-600">加载中...</p>
                        </div>
                        
                        <div v-else-if="state.error" class="text-center py-8 text-red-600">
                            {{ state.error }}
                        </div>
                        
                        <tree-node v-else
                                   v-for="item in filteredTree" 
                                   :key="item.id"
                                   :item="item"
                                   @select="selectItem">
                        </tree-node>
                    </div>
                </div>
                
                <!-- 主内容区 -->
                <div class="flex-1 flex flex-col">
                    <!-- 工具栏 -->
                    <div class="bg-white border-b border-gray-200 px-6 py-4">
                        <div class="flex items-center justify-between">
                            <button v-if="state.sidebarCollapsed" 
                                    @click="toggleSidebar"
                                    class="p-2 rounded hover:bg-gray-100">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                </svg>
                            </button>
                            
                            <h1 v-if="state.currentDocument" class="text-xl font-semibold text-gray-800">
                                {{ state.currentDocument.title }}
                            </h1>
                            
                            <div class="flex space-x-2">
                                <!-- 工具按钮可以在这里添加 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 文档内容 -->
                    <div class="flex-1 overflow-y-auto">
                        <div v-if="state.loading" class="flex items-center justify-center h-full">
                            <div class="text-center">
                                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
                                <p class="mt-4 text-gray-600">加载中...</p>
                            </div>
                        </div>
                        
                        <div v-else-if="state.currentDocument" class="h-full flex flex-col">
                            <!-- 文档头部信息 -->
                            <div class="bg-gray-50 border-b border-gray-200 px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <span class="text-2xl">{{ getDocumentIcon(state.currentDocument) }}</span>
                                        <div>
                                            <h1 class="text-xl font-semibold text-gray-900">{{ state.currentDocument.title }}</h1>
                                            <div class="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                                                <span v-if="state.currentDocument.reading_time">
                                                    📖 约 {{ state.currentDocument.reading_time }} 分钟阅读
                                                </span>
                                                <span v-if="state.currentDocument.word_count">
                                                    📝 {{ state.currentDocument.word_count }} 字
                                                </span>
                                                <span>🕒 {{ formatDate(state.currentDocument.modified) }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 操作按钮 -->
                                    <div class="flex space-x-2">
                                        <button @click="printDocument"
                                                class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded transition-colors"
                                                title="打印文档">
                                            🖨️
                                        </button>
                                        <button @click="copyLink"
                                                class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded transition-colors"
                                                title="复制链接">
                                            🔗
                                        </button>
                                    </div>
                                </div>

                                <!-- 面包屑导航 -->
                                <div v-if="state.currentDocument.parent" class="mt-3">
                                    <nav class="flex items-center space-x-2 text-sm text-gray-600">
                                        <span>📁</span>
                                        <span>{{ state.currentDocument.parent.title }}</span>
                                        <span>›</span>
                                        <span class="text-gray-900">{{ state.currentDocument.title }}</span>
                                    </nav>
                                </div>
                            </div>

                            <!-- 文档内容 -->
                            <div class="flex-1 overflow-y-auto">
                                <div class="p-6">
                                    <!-- 文档摘要 -->
                                    <div v-if="state.currentDocument.excerpt"
                                         class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
                                        <p class="text-blue-800 italic">{{ state.currentDocument.excerpt }}</p>
                                    </div>

                                    <!-- 主要内容 -->
                                    <article class="prose prose-lg max-w-none">
                                        <div v-html="state.currentDocument.content"></div>
                                    </article>

                                    <!-- 子项目列表（如果是文件夹） -->
                                    <div v-if="state.currentDocument.type === 'folder' && state.currentDocument.children && state.currentDocument.children.length > 0"
                                         class="mt-8">
                                        <h3 class="text-lg font-semibold text-gray-900 mb-4">📂 包含的项目</h3>
                                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                            <div v-for="child in state.currentDocument.children"
                                                 :key="child.id"
                                                 @click="selectItem(child)"
                                                 class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                                <div class="flex items-center space-x-3">
                                                    <span class="text-xl">{{ child.type === 'folder' ? '📁' : '📄' }}</span>
                                                    <div>
                                                        <h4 class="font-medium text-gray-900">{{ child.title }}</h4>
                                                        <p v-if="child.excerpt" class="text-sm text-gray-600 mt-1">{{ child.excerpt }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 文档元信息 -->
                                    <div class="mt-12 pt-6 border-t border-gray-200">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
                                            <div>
                                                <h4 class="font-medium text-gray-900 mb-2">文档信息</h4>
                                                <div class="space-y-1">
                                                    <p><span class="font-medium">创建时间：</span>{{ formatDate(state.currentDocument.created) }}</p>
                                                    <p><span class="font-medium">最后更新：</span>{{ formatDate(state.currentDocument.modified) }}</p>
                                                    <p v-if="state.currentDocument.author"><span class="font-medium">作者：</span>{{ state.currentDocument.author }}</p>
                                                </div>
                                            </div>
                                            <div v-if="state.currentDocument.type === 'document'">
                                                <h4 class="font-medium text-gray-900 mb-2">阅读统计</h4>
                                                <div class="space-y-1">
                                                    <p><span class="font-medium">字数：</span>{{ state.currentDocument.word_count || 0 }} 字</p>
                                                    <p><span class="font-medium">预计阅读时间：</span>{{ state.currentDocument.reading_time || 1 }} 分钟</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div v-else class="flex items-center justify-center h-full text-gray-500">
                            <div class="text-center">
                                <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <p class="text-lg">请从左侧选择一个文档查看</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `
    };
    
    // 树节点组件
    const TreeNode = {
        props: ['item'],
        emits: ['select'],
        setup(props, { emit }) {
            const expanded = ref(props.item.expanded || false);
            
            const toggle = () => {
                if (props.item.children && props.item.children.length > 0) {
                    expanded.value = !expanded.value;
                }
            };
            
            const select = () => {
                emit('select', props.item);
                if (props.item.type === 'folder') {
                    toggle();
                }
            };
            
            const getIcon = () => {
                const iconMap = {
                    'folder': expanded.value ? '📂' : '📁',
                    'document': '📄',
                    'book': '📖',
                    'guide': '📋',
                    'api': '⚙️',
                    'tutorial': '🎓',
                    'faq': '❓'
                };

                return iconMap[props.item.icon] || iconMap[props.item.type] || '📄';
            };

            const getItemClass = () => {
                let classes = 'flex items-center py-2 px-3 rounded cursor-pointer transition-all duration-200 ';

                if (props.item.type === 'folder') {
                    classes += 'hover:bg-blue-50 hover:text-blue-700 ';
                } else {
                    classes += 'hover:bg-gray-50 hover:text-gray-700 ';
                }

                return classes;
            };
            
            return {
                expanded,
                toggle,
                select,
                getIcon,
                getItemClass
            };
        },
        
        template: `
            <div class="tree-node">
                <div @click="select"
                     :class="getItemClass()"
                     :title="item.excerpt || item.title">
                    <!-- 展开/折叠图标 -->
                    <span v-if="item.children && item.children.length > 0"
                          class="mr-1 text-xs text-gray-400 transition-transform duration-200"
                          :class="{ 'rotate-90': expanded }">
                        ▶
                    </span>
                    <span v-else class="mr-3"></span>

                    <!-- 项目图标 -->
                    <span class="mr-2 text-base">{{ getIcon() }}</span>

                    <!-- 项目标题 -->
                    <span class="flex-1 text-sm font-medium"
                          :class="{ 'text-blue-700': item.type === 'folder' }">
                        {{ item.title }}
                    </span>

                    <!-- 子项目数量 -->
                    <span v-if="item.children && item.children.length > 0"
                          class="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full">
                        {{ item.children.length }}
                    </span>
                </div>

                <!-- 子项目 -->
                <transition name="slide-fade">
                    <div v-if="expanded && item.children && item.children.length > 0"
                         class="ml-4 border-l-2 border-gray-100 pl-3 mt-1">
                        <tree-node v-for="child in item.children"
                                   :key="child.id"
                                   :item="child"
                                   @select="$emit('select', $event)">
                        </tree-node>
                    </div>
                </transition>
            </div>
        `
    };
    
    // 创建并挂载应用
    const app = createApp(DocSystemApp);
    app.component('tree-node', TreeNode);
    
    // 挂载到DOM
    const container = document.getElementById('wp-doc-system-app');
    if (container) {
        app.mount(container);
    }
});
