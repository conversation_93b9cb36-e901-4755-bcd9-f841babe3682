<?php
/**
 * 修复并激活插件
 */

// 加载WordPress
require_once '../../../wp-load.php';

// 检查权限
if (!current_user_can('activate_plugins')) {
    wp_die('权限不足');
}

echo '<h1>🔧 WP Doc System 修复和激活</h1>';

// 1. 检查插件文件
echo '<h2>📁 检查插件文件</h2>';
$plugin_file = WP_PLUGIN_DIR . '/wp-doc-system/wp-doc-system.php';
if (file_exists($plugin_file)) {
    echo '<p>✅ 主插件文件存在</p>';
} else {
    echo '<p>❌ 主插件文件不存在</p>';
    exit;
}

// 2. 强制加载插件
echo '<h2>⚡ 强制加载插件</h2>';
try {
    // 直接包含插件文件
    include_once $plugin_file;
    echo '<p>✅ 插件文件加载成功</p>';
} catch (Exception $e) {
    echo '<p>❌ 插件文件加载失败：' . $e->getMessage() . '</p>';
    exit;
}

// 3. 检查主类
echo '<h2>🏗️ 检查主类</h2>';
if (class_exists('WP_Doc_System')) {
    echo '<p>✅ 主类存在</p>';
    
    // 获取实例
    $plugin_instance = WP_Doc_System::get_instance();
    echo '<p>✅ 插件实例获取成功</p>';
} else {
    echo '<p>❌ 主类不存在</p>';
    exit;
}

// 4. 手动注册自定义文章类型
echo '<h2>📝 注册自定义文章类型</h2>';
try {
    // 直接注册文章类型
    $labels = array(
        'name' => '文档项目',
        'singular_name' => '文档项目',
        'menu_name' => '文档系统',
        'add_new' => '新建',
        'add_new_item' => '新建文档项目',
        'edit_item' => '编辑文档项目',
        'view_item' => '查看文档项目',
        'all_items' => '所有项目',
    );
    
    $args = array(
        'labels' => $labels,
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_rest' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'doc-item'),
        'capability_type' => 'post',
        'has_archive' => false,
        'hierarchical' => true,
        'menu_position' => 25,
        'menu_icon' => 'dashicons-media-document',
        'supports' => array('title', 'editor', 'excerpt', 'thumbnail', 'page-attributes'),
    );
    
    register_post_type('doc_item', $args);
    echo '<p>✅ 自定义文章类型注册成功</p>';
    
    // 检查是否注册成功
    if (post_type_exists('doc_item')) {
        echo '<p>✅ doc_item 文章类型已存在</p>';
    } else {
        echo '<p>❌ doc_item 文章类型注册失败</p>';
    }
} catch (Exception $e) {
    echo '<p>❌ 文章类型注册失败：' . $e->getMessage() . '</p>';
}

// 5. 激活插件
echo '<h2>🚀 激活插件</h2>';
$plugin_path = 'wp-doc-system/wp-doc-system.php';

if (!is_plugin_active($plugin_path)) {
    $result = activate_plugin($plugin_path);
    
    if (is_wp_error($result)) {
        echo '<p>❌ 插件激活失败：' . $result->get_error_message() . '</p>';
    } else {
        echo '<p>✅ 插件激活成功</p>';
    }
} else {
    echo '<p>ℹ️ 插件已经激活</p>';
}

// 6. 刷新重写规则
flush_rewrite_rules();
echo '<p>✅ 重写规则已刷新</p>';

// 7. 最终检查
echo '<h2>🔍 最终检查</h2>';
echo '<ul>';
echo '<li>插件激活状态：' . (is_plugin_active($plugin_path) ? '✅ 已激活' : '❌ 未激活') . '</li>';
echo '<li>自定义文章类型：' . (post_type_exists('doc_item') ? '✅ 已注册' : '❌ 未注册') . '</li>';
echo '<li>主类存在：' . (class_exists('WP_Doc_System') ? '✅ 是' : '❌ 否') . '</li>';
echo '</ul>';

// 8. 创建测试数据
echo '<h2>📚 创建测试数据</h2>';
if (post_type_exists('doc_item')) {
    // 检查是否已有数据
    $existing_docs = get_posts(array(
        'post_type' => 'doc_item',
        'posts_per_page' => 1,
    ));
    
    if (empty($existing_docs)) {
        // 创建测试文件夹
        $folder_id = wp_insert_post(array(
            'post_title' => '用户指南',
            'post_type' => 'doc_item',
            'post_status' => 'publish',
            'post_content' => '这是用户指南文件夹的介绍。',
            'post_excerpt' => '包含用户相关的所有文档',
        ));
        
        if ($folder_id && !is_wp_error($folder_id)) {
            update_post_meta($folder_id, '_doc_type', 'folder');
            update_post_meta($folder_id, '_doc_icon', 'folder');
            
            // 创建测试文档
            $doc_id = wp_insert_post(array(
                'post_title' => '快速开始',
                'post_type' => 'doc_item',
                'post_status' => 'publish',
                'post_parent' => $folder_id,
                'post_content' => '<h2>欢迎使用文档系统</h2><p>这是一个测试文档，展示了文档系统的基本功能。</p><ul><li>支持层级结构</li><li>支持富文本编辑</li><li>支持搜索功能</li></ul>',
                'post_excerpt' => '快速开始使用文档系统',
            ));
            
            if ($doc_id && !is_wp_error($doc_id)) {
                update_post_meta($doc_id, '_doc_type', 'document');
                update_post_meta($doc_id, '_doc_icon', 'document');
                
                echo '<p>✅ 测试数据创建成功</p>';
                echo '<ul>';
                echo '<li>文件夹：用户指南 (ID: ' . $folder_id . ')</li>';
                echo '<li>文档：快速开始 (ID: ' . $doc_id . ')</li>';
                echo '</ul>';
            }
        }
    } else {
        echo '<p>ℹ️ 已存在文档数据，跳过创建</p>';
    }
} else {
    echo '<p>❌ 无法创建测试数据，文章类型未注册</p>';
}

// 9. 提供链接
echo '<h2>🔗 下一步操作</h2>';
echo '<div style="background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0;">';
echo '<h3>✅ 修复完成！现在您可以：</h3>';
echo '<ul>';
echo '<li><a href="' . admin_url('edit.php?post_type=doc_item') . '" target="_blank">📝 管理文档项目</a></li>';
echo '<li><a href="' . admin_url('post-new.php?post_type=doc_item') . '" target="_blank">➕ 新建文档项目</a></li>';
echo '<li><a href="test-complete.php" target="_blank">🧪 完整功能测试</a></li>';
echo '<li><a href="test-shortcode.php" target="_blank">📄 短代码测试</a></li>';
echo '</ul>';
echo '</div>';

?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    max-width: 900px;
    margin: 20px auto;
    padding: 20px;
    line-height: 1.6;
    background: #f5f5f5;
}
h1, h2, h3 { color: #333; }
ul { background: #fff; padding: 15px; border-radius: 5px; margin: 10px 0; }
li { margin: 5px 0; }
a { color: #0073aa; text-decoration: none; font-weight: bold; }
a:hover { text-decoration: underline; }
p { margin: 8px 0; }
</style>
